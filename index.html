<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>错过的星光 - Missed Starlight</title>
    <meta name="description" content="一个基于Three.js的3D互动回忆场景，重现那个永恒的仲夏夜" />
  </head>
  <body>
    <div id="app">
      <!-- 3D场景容器 -->
      <canvas id="starlight-canvas"></canvas>

      <!-- UI界面 -->
      <div id="ui-overlay">
        <!-- 歌词显示区域 -->
        <div id="lyrics-display" class="hidden">
          <p id="lyrics-text"></p>
        </div>

        <!-- 情感回响墙 -->
        <div id="echo-wall" class="hidden">
          <div class="echo-wall-content">
            <h3>匿名情感回响墙</h3>
            <p>写下你关于"错过"的故事...</p>
            <textarea id="message-input" placeholder="那些想说却没能说出口的话..."></textarea>
            <button id="submit-message">化作星光</button>
            <button id="close-echo-wall">关闭</button>
          </div>
        </div>

        <!-- 留言显示区域 -->
        <div id="message-display" class="hidden">
          <div class="message-content">
            <p id="message-text"></p>
            <span id="message-time"></span>
          </div>
        </div>

        <!-- 控制按钮 -->
        <div id="controls">
          <button id="echo-wall-btn" title="打开情感回响墙">💫</button>
          <button id="help-btn" title="使用说明">❓</button>
        </div>

        <!-- 帮助说明 -->
        <div id="help-panel" class="hidden">
          <div class="help-content">
            <h3>使用说明</h3>
            <ul>
              <li>🌟 点击星星可以看到歌词片段</li>
              <li>🌸 栀子花瓣飘落时会触发特殊效果</li>
              <li>💫 点击星光按钮可以写下匿名留言</li>
              <li>✨ 悬停在特殊星光上可以看到他人的故事</li>
              <li>🖱️ 拖拽鼠标可以改变视角</li>
            </ul>
            <button id="close-help">关闭</button>
          </div>
        </div>
      </div>

      <!-- 加载界面 -->
      <div id="loading" class="loading-screen">
        <div class="loading-content">
          <div class="loading-spinner"></div>
          <p>正在加载星光...</p>
        </div>
      </div>
    </div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>
