export class AudioManager {
  private audioContext: AudioContext | null = null;
  private isEnabled = true;
  private volume = 0.3;
  
  constructor() {
    this.initAudioContext();
  }
  
  private initAudioContext(): void {
    try {
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    } catch (error) {
      console.warn('Web Audio API not supported:', error);
    }
  }
  
  // 播放点击音效
  public playClickSound(): void {
    if (!this.isEnabled || !this.audioContext) return;
    
    this.playTone(800, 0.1, 'sine');
  }
  
  // 播放星光闪烁音效
  public playStarTwinkle(): void {
    if (!this.isEnabled || !this.audioContext) return;
    
    this.playTone(1200, 0.2, 'sine');
  }
  
  // 播放留言提交音效
  public playMessageSubmit(): void {
    if (!this.isEnabled || !this.audioContext) return;
    
    // 播放一个上升的音调序列
    setTimeout(() => this.playTone(440, 0.1, 'sine'), 0);
    setTimeout(() => this.playTone(554, 0.1, 'sine'), 100);
    setTimeout(() => this.playTone(659, 0.1, 'sine'), 200);
  }
  
  // 播放花瓣飘落音效
  public playPetalFall(): void {
    if (!this.isEnabled || !this.audioContext) return;
    
    this.playTone(330, 0.3, 'triangle');
  }
  
  // 播放悬停音效
  public playHover(): void {
    if (!this.isEnabled || !this.audioContext) return;
    
    this.playTone(600, 0.05, 'sine');
  }
  
  // 基础音调播放方法
  private playTone(frequency: number, duration: number, type: OscillatorType = 'sine'): void {
    if (!this.audioContext) return;
    
    try {
      const oscillator = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();
      
      oscillator.connect(gainNode);
      gainNode.connect(this.audioContext.destination);
      
      oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
      oscillator.type = type;
      
      // 设置音量包络
      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(this.volume, this.audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
      
      oscillator.start(this.audioContext.currentTime);
      oscillator.stop(this.audioContext.currentTime + duration);
    } catch (error) {
      console.warn('Failed to play tone:', error);
    }
  }
  
  // 播放和弦
  private playChord(frequencies: number[], duration: number, type: OscillatorType = 'sine'): void {
    frequencies.forEach(freq => {
      this.playTone(freq, duration, type);
    });
  }
  
  // 播放背景环境音效
  public playAmbientSound(): void {
    if (!this.isEnabled || !this.audioContext) return;
    
    // 播放一个低频的环境音
    this.playTone(60, 2, 'triangle');
    
    // 随机播放一些高频的星光音效
    setTimeout(() => {
      if (Math.random() > 0.7) {
        this.playStarTwinkle();
      }
    }, Math.random() * 3000);
  }
  
  // 设置音量
  public setVolume(volume: number): void {
    this.volume = Math.max(0, Math.min(1, volume));
  }
  
  // 获取音量
  public getVolume(): number {
    return this.volume;
  }
  
  // 启用/禁用音效
  public setEnabled(enabled: boolean): void {
    this.isEnabled = enabled;
  }
  
  // 检查是否启用
  public isAudioEnabled(): boolean {
    return this.isEnabled;
  }
  
  // 恢复音频上下文（用户交互后）
  public resumeAudioContext(): void {
    if (this.audioContext && this.audioContext.state === 'suspended') {
      this.audioContext.resume();
    }
  }
  
  // 创建白噪音（用于背景）
  public createWhiteNoise(duration: number = 1): void {
    if (!this.isEnabled || !this.audioContext) return;
    
    try {
      const bufferSize = this.audioContext.sampleRate * duration;
      const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
      const data = buffer.getChannelData(0);
      
      // 生成白噪音
      for (let i = 0; i < bufferSize; i++) {
        data[i] = (Math.random() * 2 - 1) * 0.1; // 低音量白噪音
      }
      
      const source = this.audioContext.createBufferSource();
      const gainNode = this.audioContext.createGain();
      
      source.buffer = buffer;
      source.connect(gainNode);
      gainNode.connect(this.audioContext.destination);
      
      gainNode.gain.setValueAtTime(this.volume * 0.1, this.audioContext.currentTime);
      
      source.start();
    } catch (error) {
      console.warn('Failed to create white noise:', error);
    }
  }
  
  // 播放特殊的栀子花音效
  public playGardeniaEffect(): void {
    if (!this.isEnabled || !this.audioContext) return;
    
    // 播放一个美妙的和弦
    const chord = [261.63, 329.63, 392.00]; // C大调和弦
    this.playChord(chord, 1.5, 'sine');
    
    // 添加一些装饰音
    setTimeout(() => this.playTone(523.25, 0.5, 'triangle'), 500);
    setTimeout(() => this.playTone(659.25, 0.5, 'triangle'), 1000);
  }
  
  // 销毁音频上下文
  public dispose(): void {
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
  }
}
