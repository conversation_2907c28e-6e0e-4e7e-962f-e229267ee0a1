// 歌词数据
export const LYRICS = [
  "总想起当天的星光",
  "栀子花白花瓣，落在我蓝色百褶裙上",
  "你都如何回忆我",
  "带着笑或是很沉默",
  "这些年来有没有人能让你不寂寞",
  "后来的我们什么都有了，却没有了我们",
  "那些年错过的大雨，那些年错过的爱情",
  "好想告诉你，如果一个人很难撑，那就换个梦想",
  "你是否一样也在静静追悔感伤",
  "如果再见不能红着眼，是否还能红着脸",
  "愿你在被打击时，记起你的珍贵，抵抗恶意",
  "愿你在迷茫时，坚信你的珍贵",
  "爱你所爱，行你所行，听从你心，无问西东",
  "那些说着永不分离的人，早已散落在天涯了",
  "我们听过无数的道理，却仍旧过不好这一生",
  "喜欢就会放肆，但爱就是克制",
  "成长就是你哪怕难过得快死掉了，但你第二天还是照常去上班",
  "有些路，只能一个人走",
  "时间会带走我们的青春，但它带不走那些美好的记忆",
  "错过的人与事，不必频频回首"
];

// 示例留言数据
export const SAMPLE_MESSAGES = [
  {
    content: "那年夏天，我们在栀子花开的路上分别，你说要去很远的地方，我说我等你回来...",
    timestamp: Date.now() - 1000 * 60 * 60 * 2 // 2小时前
  },
  {
    content: "如果时光能够倒流，我想回到那个午后，告诉你我的心意，而不是选择沉默",
    timestamp: Date.now() - 1000 * 60 * 60 * 24 // 1天前
  },
  {
    content: "总想起你笑起来的样子，像夏日里最亮的那颗星",
    timestamp: Date.now() - 1000 * 60 * 30 // 30分钟前
  },
  {
    content: "错过了你，就像错过了整个春天",
    timestamp: Date.now() - 1000 * 60 * 60 * 6 // 6小时前
  },
  {
    content: "愿你被这个世界温柔以待，愿你的眼中总有光芒",
    timestamp: Date.now() - 1000 * 60 * 15 // 15分钟前
  }
];

// 留言接口
export interface Message {
  id: string;
  content: string;
  timestamp: number;
  position: {
    x: number;
    y: number;
    z: number;
  };
}

export class DataManager {
  private static readonly STORAGE_KEY = 'starlight-messages';
  
  // 获取随机歌词
  public static getRandomLyric(): string {
    return LYRICS[Math.floor(Math.random() * LYRICS.length)];
  }
  
  // 保存留言到本地存储
  public static saveMessage(message: Message): void {
    const messages = this.getMessages();
    messages.push(message);
    localStorage.setItem(this.STORAGE_KEY, JSON.stringify(messages));
  }
  
  // 从本地存储获取所有留言
  public static getMessages(): Message[] {
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        return JSON.parse(stored);
      }
    } catch (error) {
      console.error('Failed to load messages from localStorage:', error);
    }
    
    // 如果没有存储的留言，返回示例留言
    return this.createSampleMessages();
  }
  
  // 创建示例留言
  private static createSampleMessages(): Message[] {
    return SAMPLE_MESSAGES.map((sample, index) => ({
      id: `sample-${index}`,
      content: sample.content,
      timestamp: sample.timestamp,
      position: this.generateRandomPosition()
    }));
  }
  
  // 生成随机位置
  public static generateRandomPosition(): { x: number; y: number; z: number } {
    return {
      x: (Math.random() - 0.5) * 100,
      y: (Math.random() - 0.5) * 100,
      z: (Math.random() - 0.5) * 100
    };
  }
  
  // 创建新留言
  public static createMessage(content: string): Message {
    return {
      id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
      content: content.trim(),
      timestamp: Date.now(),
      position: this.generateRandomPosition()
    };
  }
  
  // 格式化时间显示
  public static formatTimeAgo(timestamp: number): string {
    const now = Date.now();
    const diff = now - timestamp;
    const minutes = Math.floor(diff / (1000 * 60));
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));
    
    if (days > 0) {
      return `${days}天前`;
    } else if (hours > 0) {
      return `${hours}小时前`;
    } else if (minutes > 0) {
      return `${minutes}分钟前`;
    } else {
      return '刚刚';
    }
  }
  
  // 清除所有留言
  public static clearMessages(): void {
    localStorage.removeItem(this.STORAGE_KEY);
  }
  
  // 获取留言统计
  public static getMessageStats(): { total: number; today: number } {
    const messages = this.getMessages();
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayTimestamp = today.getTime();
    
    const todayMessages = messages.filter(msg => msg.timestamp >= todayTimestamp);
    
    return {
      total: messages.length,
      today: todayMessages.length
    };
  }
  
  // 验证留言内容
  public static validateMessage(content: string): { valid: boolean; error?: string } {
    const trimmed = content.trim();
    
    if (trimmed.length === 0) {
      return { valid: false, error: '请输入留言内容' };
    }
    
    if (trimmed.length > 200) {
      return { valid: false, error: '留言内容不能超过200字' };
    }
    
    if (trimmed.length < 5) {
      return { valid: false, error: '留言内容至少需要5个字符' };
    }
    
    // 检查是否包含不当内容（简单过滤）
    const forbiddenWords = ['测试', 'test', '111', '222', '333'];
    const hasForbiddenWord = forbiddenWords.some(word => 
      trimmed.toLowerCase().includes(word.toLowerCase())
    );
    
    if (hasForbiddenWord) {
      return { valid: false, error: '请输入有意义的留言内容' };
    }
    
    return { valid: true };
  }
  
  // 获取留言预览（截取前30个字符）
  public static getMessagePreview(content: string): string {
    if (content.length <= 30) {
      return content;
    }
    return content.substring(0, 30) + '...';
  }
}
