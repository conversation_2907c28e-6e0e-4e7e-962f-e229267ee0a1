import * as THREE from 'three';
import { gsap } from 'gsap';
import { DataManager, Message } from './DataManager';
import { AudioManager } from './AudioManager';

export class StarScene {
  private scene: THREE.Scene;
  private camera: THREE.PerspectiveCamera;
  private renderer: THREE.WebGLRenderer;
  private canvas: HTMLCanvasElement;
  
  // 星空相关
  private stars: THREE.Points[] = [];
  private starGeometry: THREE.BufferGeometry;
  private starMaterial: THREE.PointsMaterial;
  
  // 特殊星光（留言星）
  private messageStars: THREE.Points[] = [];
  private messages: Message[] = [];
  
  // 栀子花瓣
  private petals: THREE.Points;
  private petalPositions: Float32Array;
  private petalVelocities: Float32Array;
  
  // 交互相关
  private raycaster: THREE.Raycaster;
  private mouse: THREE.Vector2;
  private isMouseDown = false;
  private mouseDownPosition = new THREE.Vector2();
  
  // 相机控制
  private cameraTarget = new THREE.Vector3(0, 0, 0);
  private cameraRadius = 50;
  private cameraTheta = 0;
  private cameraPhi = Math.PI / 2;

  // 音效管理器
  private audioManager: AudioManager;
  
  constructor(canvas: HTMLCanvasElement) {
    this.canvas = canvas;
    this.raycaster = new THREE.Raycaster();
    this.mouse = new THREE.Vector2();
    this.audioManager = new AudioManager();

    this.initScene();
    this.initCamera();
    this.initRenderer();
    this.initLights();
    this.initStars();
    this.initPetals();
    this.initEventListeners();
    this.loadMessages();

    this.animate();

    // 启动环境音效
    setTimeout(() => {
      this.audioManager.playAmbientSound();
    }, 2000);
  }
  
  private initScene(): void {
    this.scene = new THREE.Scene();
    
    // 创建星空背景
    const loader = new THREE.CubeTextureLoader();
    const texture = loader.load([
      'data:image/svg+xml;base64,' + btoa('<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"><rect width="1" height="1" fill="#000428"/></svg>'),
      'data:image/svg+xml;base64,' + btoa('<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"><rect width="1" height="1" fill="#000428"/></svg>'),
      'data:image/svg+xml;base64,' + btoa('<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"><rect width="1" height="1" fill="#004e92"/></svg>'),
      'data:image/svg+xml;base64,' + btoa('<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"><rect width="1" height="1" fill="#000428"/></svg>'),
      'data:image/svg+xml;base64,' + btoa('<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"><rect width="1" height="1" fill="#000428"/></svg>'),
      'data:image/svg+xml;base64,' + btoa('<svg xmlns="http://www.w3.org/2000/svg" width="1" height="1"><rect width="1" height="1" fill="#000428"/></svg>')
    ]);
    this.scene.background = texture;
  }
  
  private initCamera(): void {
    this.camera = new THREE.PerspectiveCamera(
      75,
      window.innerWidth / window.innerHeight,
      0.1,
      1000
    );
    this.updateCameraPosition();
  }
  
  private initRenderer(): void {
    this.renderer = new THREE.WebGLRenderer({
      canvas: this.canvas,
      antialias: true,
      alpha: true
    });
    this.renderer.setSize(window.innerWidth, window.innerHeight);
    this.renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2));
  }
  
  private initLights(): void {
    // 环境光
    const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
    this.scene.add(ambientLight);
    
    // 点光源
    const pointLight = new THREE.PointLight(0xffffff, 0.5, 100);
    pointLight.position.set(10, 10, 10);
    this.scene.add(pointLight);
  }
  
  private initStars(): void {
    const starCount = 2000;
    const positions = new Float32Array(starCount * 3);
    const colors = new Float32Array(starCount * 3);
    const sizes = new Float32Array(starCount);
    
    for (let i = 0; i < starCount; i++) {
      // 随机位置
      positions[i * 3] = (Math.random() - 0.5) * 200;
      positions[i * 3 + 1] = (Math.random() - 0.5) * 200;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 200;
      
      // 随机颜色（偏蓝白色）
      const color = new THREE.Color();
      color.setHSL(0.6 + Math.random() * 0.1, 0.3 + Math.random() * 0.3, 0.8 + Math.random() * 0.2);
      colors[i * 3] = color.r;
      colors[i * 3 + 1] = color.g;
      colors[i * 3 + 2] = color.b;
      
      // 随机大小
      sizes[i] = Math.random() * 3 + 1;
    }
    
    this.starGeometry = new THREE.BufferGeometry();
    this.starGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    this.starGeometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));
    this.starGeometry.setAttribute('size', new THREE.BufferAttribute(sizes, 1));
    
    this.starMaterial = new THREE.PointsMaterial({
      size: 2,
      sizeAttenuation: true,
      vertexColors: true,
      transparent: true,
      opacity: 0.8,
      blending: THREE.AdditiveBlending
    });
    
    const starField = new THREE.Points(this.starGeometry, this.starMaterial);
    this.scene.add(starField);
    this.stars.push(starField);
  }
  
  private initPetals(): void {
    const petalCount = 50;
    const positions = new Float32Array(petalCount * 3);
    const velocities = new Float32Array(petalCount * 3);
    
    for (let i = 0; i < petalCount; i++) {
      // 初始位置在场景上方
      positions[i * 3] = (Math.random() - 0.5) * 100;
      positions[i * 3 + 1] = 50 + Math.random() * 20;
      positions[i * 3 + 2] = (Math.random() - 0.5) * 100;
      
      // 下落速度
      velocities[i * 3] = (Math.random() - 0.5) * 0.1;
      velocities[i * 3 + 1] = -0.05 - Math.random() * 0.05;
      velocities[i * 3 + 2] = (Math.random() - 0.5) * 0.1;
    }
    
    this.petalPositions = positions;
    this.petalVelocities = velocities;
    
    const petalGeometry = new THREE.BufferGeometry();
    petalGeometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
    
    const petalMaterial = new THREE.PointsMaterial({
      color: 0xffffff,
      size: 4,
      sizeAttenuation: true,
      transparent: true,
      opacity: 0.7,
      blending: THREE.AdditiveBlending
    });
    
    this.petals = new THREE.Points(petalGeometry, petalMaterial);
    this.scene.add(this.petals);
  }
  
  private initEventListeners(): void {
    // 鼠标事件
    this.canvas.addEventListener('mousedown', this.onMouseDown.bind(this));
    this.canvas.addEventListener('mousemove', this.onMouseMove.bind(this));
    this.canvas.addEventListener('mouseup', this.onMouseUp.bind(this));
    this.canvas.addEventListener('click', this.onClick.bind(this));
    
    // 触摸事件
    this.canvas.addEventListener('touchstart', this.onTouchStart.bind(this));
    this.canvas.addEventListener('touchmove', this.onTouchMove.bind(this));
    this.canvas.addEventListener('touchend', this.onTouchEnd.bind(this));
    
    // 窗口大小变化
    window.addEventListener('resize', this.onWindowResize.bind(this));
  }
  
  private updateCameraPosition(): void {
    this.camera.position.x = this.cameraTarget.x + this.cameraRadius * Math.sin(this.cameraPhi) * Math.cos(this.cameraTheta);
    this.camera.position.y = this.cameraTarget.y + this.cameraRadius * Math.cos(this.cameraPhi);
    this.camera.position.z = this.cameraTarget.z + this.cameraRadius * Math.sin(this.cameraPhi) * Math.sin(this.cameraTheta);
    this.camera.lookAt(this.cameraTarget);
  }
  
  private onMouseDown(event: MouseEvent): void {
    this.isMouseDown = true;
    this.mouseDownPosition.set(event.clientX, event.clientY);
  }
  
  private onMouseMove(event: MouseEvent): void {
    if (this.isMouseDown) {
      const deltaX = event.clientX - this.mouseDownPosition.x;
      const deltaY = event.clientY - this.mouseDownPosition.y;

      this.cameraTheta += deltaX * 0.01;
      this.cameraPhi += deltaY * 0.01;
      this.cameraPhi = Math.max(0.1, Math.min(Math.PI - 0.1, this.cameraPhi));

      this.updateCameraPosition();

      this.mouseDownPosition.set(event.clientX, event.clientY);
    } else {
      // 检查鼠标悬停
      this.checkHover(event);
    }
  }

  private checkHover(event: MouseEvent): void {
    // 更新鼠标位置
    this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

    // 射线检测
    this.raycaster.setFromCamera(this.mouse, this.camera);
    const intersects = this.raycaster.intersectObjects(this.messageStars);

    if (intersects.length > 0) {
      // 悬停在留言星上
      this.canvas.style.cursor = 'pointer';
      this.showHoverPreview(intersects[0]);
      this.audioManager.playHover();
    } else {
      // 没有悬停在留言星上
      this.canvas.style.cursor = 'grab';
      this.hideHoverPreview();
    }
  }

  private showHoverPreview(intersection: THREE.Intersection): void {
    const starIndex = this.messageStars.indexOf(intersection.object as THREE.Points);
    if (starIndex >= 0 && starIndex < this.messages.length) {
      const message = this.messages[starIndex];
      // 这里可以显示一个小的预览提示
      // 暂时通过改变鼠标样式来提示
    }
  }

  private hideHoverPreview(): void {
    // 隐藏悬停预览
  }
  
  private onMouseUp(): void {
    this.isMouseDown = false;
  }
  
  private onClick(event: MouseEvent): void {
    // 恢复音频上下文（用户交互后）
    this.audioManager.resumeAudioContext();

    // 更新鼠标位置
    this.mouse.x = (event.clientX / window.innerWidth) * 2 - 1;
    this.mouse.y = -(event.clientY / window.innerHeight) * 2 + 1;

    // 射线检测
    this.raycaster.setFromCamera(this.mouse, this.camera);
    const intersects = this.raycaster.intersectObjects([...this.stars, ...this.messageStars]);

    if (intersects.length > 0) {
      this.onStarClick(intersects[0]);
    }
  }
  
  private onStarClick(intersection: THREE.Intersection): void {
    // 播放点击音效
    this.audioManager.playClickSound();

    // 检查是否是留言星
    const isMessageStar = this.messageStars.includes(intersection.object as THREE.Points);

    if (isMessageStar) {
      // 显示留言内容
      this.showMessage(intersection);
    } else {
      // 显示随机歌词
      this.showLyrics();
      this.audioManager.playStarTwinkle();
    }
  }
  
  private showLyrics(): void {
    const randomLyric = DataManager.getRandomLyric();
    const lyricsDisplay = document.getElementById('lyrics-display');
    const lyricsText = document.getElementById('lyrics-text');

    if (lyricsDisplay && lyricsText) {
      lyricsText.textContent = randomLyric;
      lyricsDisplay.classList.remove('hidden');

      // 3秒后自动隐藏
      setTimeout(() => {
        lyricsDisplay.classList.add('hidden');
      }, 3000);
    }
  }
  
  private showMessage(intersection: THREE.Intersection): void {
    // 根据点击的星星找到对应的留言
    const starIndex = this.messageStars.indexOf(intersection.object as THREE.Points);
    if (starIndex >= 0 && starIndex < this.messages.length) {
      const message = this.messages[starIndex];
      const messageDisplay = document.getElementById('message-display');
      const messageText = document.getElementById('message-text');
      const messageTime = document.getElementById('message-time');

      if (messageDisplay && messageText && messageTime) {
        messageText.textContent = message.content;
        messageTime.textContent = DataManager.formatTimeAgo(message.timestamp);
        messageDisplay.classList.remove('hidden');

        // 5秒后自动隐藏
        setTimeout(() => {
          messageDisplay.classList.add('hidden');
        }, 5000);
      }
    }
  }
  
  private onTouchStart(event: TouchEvent): void {
    if (event.touches.length === 1) {
      const touch = event.touches[0];
      this.mouseDownPosition.set(touch.clientX, touch.clientY);
      this.isMouseDown = true;
    }
  }
  
  private onTouchMove(event: TouchEvent): void {
    if (event.touches.length === 1 && this.isMouseDown) {
      const touch = event.touches[0];
      const deltaX = touch.clientX - this.mouseDownPosition.x;
      const deltaY = touch.clientY - this.mouseDownPosition.y;
      
      this.cameraTheta += deltaX * 0.01;
      this.cameraPhi += deltaY * 0.01;
      this.cameraPhi = Math.max(0.1, Math.min(Math.PI - 0.1, this.cameraPhi));
      
      this.updateCameraPosition();
      
      this.mouseDownPosition.set(touch.clientX, touch.clientY);
    }
  }
  
  private onTouchEnd(): void {
    this.isMouseDown = false;
  }
  
  private onWindowResize(): void {
    this.camera.aspect = window.innerWidth / window.innerHeight;
    this.camera.updateProjectionMatrix();
    this.renderer.setSize(window.innerWidth, window.innerHeight);
  }
  
  private loadMessages(): void {
    // 从DataManager加载留言
    this.messages = DataManager.getMessages().map(msg => ({
      ...msg,
      position: new THREE.Vector3(msg.position.x, msg.position.y, msg.position.z)
    }));
    this.createMessageStars();
  }
  
  private createMessageStars(): void {
    // 清除现有的留言星
    this.messageStars.forEach(star => {
      this.scene.remove(star);
      star.geometry.dispose();
      if (star.material instanceof THREE.Material) {
        star.material.dispose();
      }
    });
    this.messageStars = [];

    // 为每条留言创建特殊的星光
    this.messages.forEach(message => {
      const geometry = new THREE.BufferGeometry();
      const positions = new Float32Array([
        message.position.x,
        message.position.y,
        message.position.z
      ]);
      geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));

      const material = new THREE.PointsMaterial({
        color: 0xffd700, // 金色
        size: 5,
        sizeAttenuation: true,
        transparent: true,
        opacity: 0.9,
        blending: THREE.AdditiveBlending
      });

      const messageStar = new THREE.Points(geometry, material);
      this.scene.add(messageStar);
      this.messageStars.push(messageStar);
    });
  }
  
  public addMessage(content: string): void {
    const newMessage = DataManager.createMessage(content);
    const message: Message = {
      ...newMessage,
      position: new THREE.Vector3(
        newMessage.position.x,
        newMessage.position.y,
        newMessage.position.z
      )
    };

    this.messages.push(message);

    // 保存到DataManager
    DataManager.saveMessage(newMessage);

    // 创建新的留言星
    this.createMessageStars();
  }
  
  private updatePetals(): void {
    const positions = this.petals.geometry.attributes.position.array as Float32Array;
    
    for (let i = 0; i < positions.length; i += 3) {
      // 更新位置
      positions[i] += this.petalVelocities[i];
      positions[i + 1] += this.petalVelocities[i + 1];
      positions[i + 2] += this.petalVelocities[i + 2];
      
      // 如果花瓣落到底部，重新生成
      if (positions[i + 1] < -50) {
        positions[i] = (Math.random() - 0.5) * 100;
        positions[i + 1] = 50 + Math.random() * 20;
        positions[i + 2] = (Math.random() - 0.5) * 100;
      }
      
      // 检查是否落在中央区域
      if (positions[i + 1] < 0 && positions[i + 1] > -5 &&
          Math.abs(positions[i]) < 5 && Math.abs(positions[i + 2]) < 5) {
        this.triggerPetalEffect();
      }
    }
    
    this.petals.geometry.attributes.position.needsUpdate = true;
  }
  
  private triggerPetalEffect(): void {
    // 触发栀子花瓣效果
    console.log('栀子花白花瓣，落在我蓝色百褶裙上');
    // 这里可以添加特殊效果，比如显示文字或播放音效
  }
  
  private animate(): void {
    requestAnimationFrame(this.animate.bind(this));
    
    // 更新花瓣
    this.updatePetals();
    
    // 星星闪烁效果
    const time = Date.now() * 0.001;
    this.stars.forEach(starField => {
      if (starField.material instanceof THREE.PointsMaterial) {
        starField.material.opacity = 0.5 + 0.3 * Math.sin(time * 2);
      }
    });
    
    // 留言星闪烁效果
    this.messageStars.forEach(messageStar => {
      if (messageStar.material instanceof THREE.PointsMaterial) {
        messageStar.material.opacity = 0.7 + 0.3 * Math.sin(time * 3);
      }
    });
    
    this.renderer.render(this.scene, this.camera);
  }
  
  public dispose(): void {
    // 清理资源
    this.scene.clear();
    this.renderer.dispose();
  }
}
