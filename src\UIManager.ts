import { DataManager } from './DataManager';

export class UIManager {
  private echoWallBtn: HTMLElement | null;
  private helpBtn: HTMLElement | null;
  private echoWall: HTMLElement | null;
  private helpPanel: HTMLElement | null;
  private messageInput: HTMLTextAreaElement | null;
  private submitBtn: HTMLElement | null;
  private closeEchoWallBtn: HTMLElement | null;
  private closeHelpBtn: HTMLElement | null;
  private lyricsDisplay: HTMLElement | null;
  private messageDisplay: HTMLElement | null;
  private loading: HTMLElement | null;
  
  private onMessageSubmit: ((message: string) => void) | null = null;
  
  constructor() {
    this.initElements();
    this.initEventListeners();
  }
  
  private initElements(): void {
    this.echoWallBtn = document.getElementById('echo-wall-btn');
    this.helpBtn = document.getElementById('help-btn');
    this.echoWall = document.getElementById('echo-wall');
    this.helpPanel = document.getElementById('help-panel');
    this.messageInput = document.getElementById('message-input') as HTMLTextAreaElement;
    this.submitBtn = document.getElementById('submit-message');
    this.closeEchoWallBtn = document.getElementById('close-echo-wall');
    this.closeHelpBtn = document.getElementById('close-help');
    this.lyricsDisplay = document.getElementById('lyrics-display');
    this.messageDisplay = document.getElementById('message-display');
    this.loading = document.getElementById('loading');
  }
  
  private initEventListeners(): void {
    // 情感回响墙按钮
    this.echoWallBtn?.addEventListener('click', () => {
      this.showEchoWall();
    });
    
    // 帮助按钮
    this.helpBtn?.addEventListener('click', () => {
      this.toggleHelpPanel();
    });
    
    // 提交留言按钮
    this.submitBtn?.addEventListener('click', () => {
      this.submitMessage();
    });
    
    // 关闭情感回响墙
    this.closeEchoWallBtn?.addEventListener('click', () => {
      this.hideEchoWall();
    });
    
    // 关闭帮助面板
    this.closeHelpBtn?.addEventListener('click', () => {
      this.hideHelpPanel();
    });
    
    // 点击歌词显示区域关闭
    this.lyricsDisplay?.addEventListener('click', () => {
      this.hideLyrics();
    });
    
    // 点击留言显示区域关闭
    this.messageDisplay?.addEventListener('click', () => {
      this.hideMessage();
    });
    
    // 键盘事件
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        this.hideAllPanels();
      }
    });
    
    // 输入框回车提交（Ctrl+Enter）
    this.messageInput?.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' && event.ctrlKey) {
        event.preventDefault();
        this.submitMessage();
      }
    });
  }
  
  public showEchoWall(): void {
    this.echoWall?.classList.remove('hidden');
    this.messageInput?.focus();
  }
  
  public hideEchoWall(): void {
    this.echoWall?.classList.add('hidden');
    if (this.messageInput) {
      this.messageInput.value = '';
    }
  }
  
  public showHelpPanel(): void {
    this.helpPanel?.classList.remove('hidden');
  }
  
  public hideHelpPanel(): void {
    this.helpPanel?.classList.add('hidden');
  }
  
  public toggleHelpPanel(): void {
    if (this.helpPanel?.classList.contains('hidden')) {
      this.showHelpPanel();
    } else {
      this.hideHelpPanel();
    }
  }
  
  public showLyrics(text: string): void {
    const lyricsText = document.getElementById('lyrics-text');
    if (lyricsText && this.lyricsDisplay) {
      lyricsText.textContent = text;
      this.lyricsDisplay.classList.remove('hidden');
      
      // 3秒后自动隐藏
      setTimeout(() => {
        this.hideLyrics();
      }, 3000);
    }
  }
  
  public hideLyrics(): void {
    this.lyricsDisplay?.classList.add('hidden');
  }
  
  public showMessage(content: string, time: string): void {
    const messageText = document.getElementById('message-text');
    const messageTime = document.getElementById('message-time');
    
    if (messageText && messageTime && this.messageDisplay) {
      messageText.textContent = content;
      messageTime.textContent = time;
      this.messageDisplay.classList.remove('hidden');
      
      // 5秒后自动隐藏
      setTimeout(() => {
        this.hideMessage();
      }, 5000);
    }
  }
  
  public hideMessage(): void {
    this.messageDisplay?.classList.add('hidden');
  }
  
  public hideAllPanels(): void {
    this.hideEchoWall();
    this.hideHelpPanel();
    this.hideLyrics();
    this.hideMessage();
  }
  
  private submitMessage(): void {
    if (!this.messageInput) return;

    const message = this.messageInput.value.trim();

    // 使用DataManager验证留言
    const validation = DataManager.validateMessage(message);
    if (!validation.valid) {
      this.showNotification(validation.error || '留言格式不正确');
      return;
    }

    // 调用回调函数
    if (this.onMessageSubmit) {
      this.onMessageSubmit(message);
    }

    // 显示成功提示
    this.showNotification('留言已化作星光 ✨');

    // 关闭情感回响墙
    this.hideEchoWall();
  }
  
  public setMessageSubmitCallback(callback: (message: string) => void): void {
    this.onMessageSubmit = callback;
  }
  
  public showNotification(message: string, duration: number = 2000): void {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = 'notification';
    notification.textContent = message;
    notification.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(0, 0, 30, 0.9);
      color: rgba(255, 255, 255, 0.9);
      padding: 15px 25px;
      border-radius: 25px;
      border: 1px solid rgba(74, 144, 226, 0.3);
      backdrop-filter: blur(10px);
      z-index: 1001;
      font-family: inherit;
      font-size: 14px;
      pointer-events: none;
      animation: fadeInScale 0.3s ease-out;
    `;
    
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
      notification.style.animation = 'fadeOutScale 0.3s ease-in forwards';
      setTimeout(() => {
        document.body.removeChild(notification);
      }, 300);
    }, duration);
  }
  
  public showLoading(): void {
    this.loading?.classList.remove('hidden');
  }
  
  public hideLoading(): void {
    this.loading?.classList.add('hidden');
  }
  
  public updateLoadingText(text: string): void {
    const loadingText = this.loading?.querySelector('p');
    if (loadingText) {
      loadingText.textContent = text;
    }
  }
  
  // 响应式处理
  public handleResize(): void {
    // 在移动设备上调整UI
    const isMobile = window.innerWidth <= 768;
    
    if (isMobile) {
      // 移动设备特殊处理
      const controls = document.getElementById('controls');
      if (controls) {
        controls.style.bottom = '20px';
        controls.style.right = '20px';
      }
      
      const helpPanel = document.getElementById('help-panel');
      if (helpPanel) {
        helpPanel.style.bottom = '80px';
        helpPanel.style.right = '20px';
        helpPanel.style.minWidth = '200px';
      }
    }
  }
  
  // 添加触摸反馈
  public addTouchFeedback(): void {
    const buttons = document.querySelectorAll('button');
    buttons.forEach(button => {
      button.addEventListener('touchstart', () => {
        button.style.transform = 'scale(0.95)';
      });
      
      button.addEventListener('touchend', () => {
        button.style.transform = '';
      });
    });
  }
  
  // 初始化完成后的设置
  public init(): void {
    this.handleResize();
    this.addTouchFeedback();
    
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      this.handleResize();
    });
    
    // 隐藏加载界面
    setTimeout(() => {
      this.hideLoading();
    }, 1000);
  }
}
