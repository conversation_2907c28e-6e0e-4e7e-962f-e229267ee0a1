import './style.css';
import { StarScene } from './StarScene';
import { UIManager } from './UIManager';

class MissedStarlightApp {
  private starScene: StarScene | null = null;
  private uiManager: UIManager | null = null;
  private canvas: HTMLCanvasElement | null = null;

  constructor() {
    this.init();
  }

  private async init(): Promise<void> {
    try {
      // 获取画布元素
      this.canvas = document.getElementById('starlight-canvas') as HTMLCanvasElement;
      if (!this.canvas) {
        throw new Error('Canvas element not found');
      }

      // 初始化UI管理器
      this.uiManager = new UIManager();
      this.uiManager.showLoading();
      this.uiManager.updateLoadingText('正在初始化星空...');

      // 等待一小段时间让加载界面显示
      await this.delay(500);

      // 初始化3D场景
      this.uiManager.updateLoadingText('正在创建星光...');
      this.starScene = new StarScene(this.canvas);

      await this.delay(500);

      // 设置UI回调
      this.setupUICallbacks();

      // 初始化UI
      this.uiManager.updateLoadingText('准备就绪...');
      await this.delay(300);

      this.uiManager.init();

      console.log('错过的星光应用初始化完成');

    } catch (error) {
      console.error('应用初始化失败:', error);
      this.showError('应用初始化失败，请刷新页面重试');
    }
  }

  private setupUICallbacks(): void {
    if (!this.uiManager || !this.starScene) return;

    // 设置留言提交回调
    this.uiManager.setMessageSubmitCallback((message: string) => {
      if (this.starScene) {
        this.starScene.addMessage(message);
      }
    });
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private showError(message: string): void {
    const errorDiv = document.createElement('div');
    errorDiv.style.cssText = `
      position: fixed;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      background: rgba(139, 0, 0, 0.9);
      color: white;
      padding: 20px;
      border-radius: 10px;
      text-align: center;
      z-index: 2000;
      font-family: inherit;
    `;
    errorDiv.innerHTML = `
      <h3>错误</h3>
      <p>${message}</p>
      <button onclick="location.reload()" style="
        background: white;
        color: #8b0000;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        cursor: pointer;
        margin-top: 10px;
      ">刷新页面</button>
    `;
    document.body.appendChild(errorDiv);
  }

  // 清理资源
  public dispose(): void {
    if (this.starScene) {
      this.starScene.dispose();
    }
  }
}

// 应用入口
document.addEventListener('DOMContentLoaded', () => {
  const app = new MissedStarlightApp();

  // 页面卸载时清理资源
  window.addEventListener('beforeunload', () => {
    app.dispose();
  });
});

// 添加一些CSS动画
const style = document.createElement('style');
style.textContent = `
  @keyframes fadeOutScale {
    from {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    to {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.8);
    }
  }
`;
document.head.appendChild(style);
