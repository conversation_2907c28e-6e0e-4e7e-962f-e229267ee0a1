/* 错过的星光 - 样式文件 */
:root {
  font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  --primary-color: #4a90e2;
  --secondary-color: #f5a623;
  --text-light: rgba(255, 255, 255, 0.9);
  --text-dim: rgba(255, 255, 255, 0.6);
  --bg-dark: rgba(0, 0, 20, 0.8);
  --bg-panel: rgba(0, 0, 30, 0.9);
  --border-glow: rgba(74, 144, 226, 0.3);
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  margin: 0;
  overflow: hidden;
  background: linear-gradient(to bottom, #000428, #004e92);
  font-family: inherit;
}

#app {
  position: relative;
  width: 100vw;
  height: 100vh;
}

/* 3D场景画布 */
#starlight-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
  cursor: grab;
}

#starlight-canvas:active {
  cursor: grabbing;
}

/* UI覆盖层 */
#ui-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 100;
}

/* 通用隐藏类 */
.hidden {
  display: none !important;
}

/* 歌词显示 */
#lyrics-display {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--bg-panel);
  border: 1px solid var(--border-glow);
  border-radius: 15px;
  padding: 20px 30px;
  backdrop-filter: blur(10px);
  pointer-events: auto;
  animation: fadeInScale 0.5s ease-out;
}

#lyrics-text {
  color: var(--text-light);
  font-size: 18px;
  text-align: center;
  margin: 0;
  text-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
}

/* 情感回响墙 */
#echo-wall {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--bg-panel);
  border: 1px solid var(--border-glow);
  border-radius: 20px;
  padding: 30px;
  backdrop-filter: blur(15px);
  pointer-events: auto;
  min-width: 400px;
  max-width: 500px;
  animation: fadeInScale 0.5s ease-out;
}

.echo-wall-content h3 {
  color: var(--text-light);
  margin-bottom: 10px;
  text-align: center;
  font-size: 20px;
}

.echo-wall-content p {
  color: var(--text-dim);
  margin-bottom: 20px;
  text-align: center;
  font-size: 14px;
}

#message-input {
  width: 100%;
  height: 120px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-glow);
  border-radius: 10px;
  padding: 15px;
  color: var(--text-light);
  font-family: inherit;
  font-size: 14px;
  resize: none;
  margin-bottom: 20px;
}

#message-input::placeholder {
  color: var(--text-dim);
}

#message-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 15px rgba(74, 144, 226, 0.3);
}

/* 按钮样式 */
button {
  background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
  border: none;
  border-radius: 25px;
  padding: 12px 24px;
  color: white;
  font-family: inherit;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  margin: 5px;
  pointer-events: auto;
}

button:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
}

button:active {
  transform: translateY(0);
}

#submit-message {
  width: 100%;
  margin-bottom: 10px;
}

#close-echo-wall {
  background: rgba(255, 255, 255, 0.2);
  width: 100%;
}

/* 留言显示 */
#message-display {
  position: absolute;
  top: 20px;
  right: 20px;
  background: var(--bg-panel);
  border: 1px solid var(--border-glow);
  border-radius: 15px;
  padding: 15px 20px;
  backdrop-filter: blur(10px);
  pointer-events: auto;
  max-width: 300px;
  animation: fadeInSlide 0.5s ease-out;
}

.message-content p {
  color: var(--text-light);
  font-size: 14px;
  margin: 0 0 10px 0;
  line-height: 1.4;
}

.message-content span {
  color: var(--text-dim);
  font-size: 12px;
}

/* 控制按钮 */
#controls {
  position: absolute;
  bottom: 30px;
  right: 30px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

#controls button {
  width: 50px;
  height: 50px;
  border-radius: 50%;
  font-size: 20px;
  padding: 0;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 帮助面板 */
#help-panel {
  position: absolute;
  bottom: 100px;
  right: 30px;
  background: var(--bg-panel);
  border: 1px solid var(--border-glow);
  border-radius: 15px;
  padding: 20px;
  backdrop-filter: blur(10px);
  pointer-events: auto;
  min-width: 250px;
  animation: fadeInSlide 0.5s ease-out;
}

.help-content h3 {
  color: var(--text-light);
  margin-bottom: 15px;
  font-size: 16px;
}

.help-content ul {
  list-style: none;
  margin-bottom: 15px;
}

.help-content li {
  color: var(--text-dim);
  font-size: 13px;
  margin-bottom: 8px;
  line-height: 1.4;
}

#close-help {
  width: 100%;
  background: rgba(255, 255, 255, 0.2);
}

/* 加载界面 */
.loading-screen {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, #000428, #004e92);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  text-align: center;
  color: var(--text-light);
}

.loading-spinner {
  width: 50px;
  height: 50px;
  border: 3px solid rgba(255, 255, 255, 0.3);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

.loading-content p {
  font-size: 16px;
  margin: 0;
}

/* 动画 */
@keyframes fadeInScale {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes fadeInSlide {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  #echo-wall {
    min-width: 90vw;
    max-width: 90vw;
    padding: 20px;
  }

  #controls {
    bottom: 20px;
    right: 20px;
  }

  #help-panel {
    bottom: 80px;
    right: 20px;
    min-width: 200px;
  }

  #message-display {
    top: 20px;
    right: 20px;
    max-width: 250px;
  }
}